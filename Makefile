.PHONY: help install dev build start clean lint format check test

# Default target
help:
	@echo "Available commands:"
	@echo "  install     - Install dependencies"
	@echo "  dev         - Start development server"
	@echo "  build       - Build for production"
	@echo "  start       - Start production server"
	@echo "  clean       - Clean build artifacts"
	@echo "  lint        - Run ESLint"
	@echo "  format      - Format code with <PERSON>ttier"
	@echo "  check       - Run all checks (type, lint, format)"
	@echo "  setup       - Initial project setup"

# Install dependencies
install:
	yarn install

# Start development server
dev:
	yarn dev

# Build for production
build:
	yarn build

# Start production server
start:
	yarn start

# Clean build artifacts
clean:
	yarn clean

# Run linting
lint:
	yarn lint

# Format code
format:
	yarn format

# Run all checks
check:
	yarn check-all

# Initial project setup
setup:
	@echo "Setting up project..."
	yarn install
	@if [ ! -f .env ]; then cp .env.example .env; echo "Created .env file from .env.example"; fi
	@echo "Setup complete! Please configure your .env file."
