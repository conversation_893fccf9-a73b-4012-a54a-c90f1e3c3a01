export interface AppConfig {
    name: string;
    version: string;
    environment: string;
    port: number;
}
export interface ZaloConfig {
    appId: string;
    secretKey: string;
    oaId: string;
    webhookUrl: string;
}
export interface LoggingConfig {
    level: string;
    format: string;
}
export interface Config {
    app: AppConfig;
    zalo: ZaloConfig;
    logging: LoggingConfig;
}
//# sourceMappingURL=config.d.ts.map