import { ZaloConfig, ZaloWebhookEvent } from '../types';
export declare class ZaloBot {
    private config;
    private isInitialized;
    constructor(config: ZaloConfig);
    initialize(): Promise<void>;
    private validateConfig;
    handleWebhookEvent(event: ZaloWebhookEvent): Promise<void>;
    private handleTextMessage;
    private handleImageMessage;
    sendTextMessage(userId: string, text: string): Promise<void>;
}
//# sourceMappingURL=ZaloBot.d.ts.map