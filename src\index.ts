import { ZaloBot } from './services/ZaloBot';
import { config } from './config/config';
import { logger } from './utils/logger';

async function main(): Promise<void> {
  try {
    logger.info('🚀 Starting Zalo Chatbot...');
    
    const bot = new ZaloBot(config.zalo);
    await bot.initialize();
    
    logger.info('✅ Zalo Chatbot started successfully!');
  } catch (error) {
    logger.error('❌ Failed to start Zalo Chatbot:', error);
    process.exit(1);
  }
}

// Graceful shutdown
process.on('SIGINT', () => {
  logger.info('🛑 Shutting down gracefully...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  logger.info('🛑 Shutting down gracefully...');
  process.exit(0);
});

// Start the application
main().catch(error => {
  logger.error('💥 Unhandled error:', error);
  process.exit(1);
});
